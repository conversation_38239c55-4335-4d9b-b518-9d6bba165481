package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springblade.modules.hzyc.DocumentGeneration.util.DictCodeConverter;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;
/**
 * 送达回证文档生成实现类
 *
 * <AUTHOR>
 */
@Service("deliveryReceiptDocument")
public class DeliveryReceiptDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "45送达回证.docx";
    }

    @Override
    public String getDocumentType() {
        return "DELIVERY-RECEIPT";
    }

	public static Map<String, String> getReverseFieldMapping() {
		Map<String, String> mapping = new HashMap<>();

		mapping.put("FJID", "file_uuids");
		mapping.put("SDNRSDWSMCBH", "service_content");
		mapping.put("WSRQ", "doc_date");
		mapping.put("DSR", "party");
		mapping.put("SDNRJSONZFC", "delivery_json");
		mapping.put("SDR", "to_receive_person");
		mapping.put("CJSJ", "create_time");
		mapping.put("SDHZBS", "service_receipt_uuid");
		mapping.put("WSSDFS", "doc_send_type");
		mapping.put("SFQY", "is_active");
		mapping.put("SSRQZ", "receiver_name");
		mapping.put("SQJLUUID", "handle_main_uuid");
		mapping.put("SFJSDZSD", "is_accept_elec_delivery");
		mapping.put("XTGXSJCXBYDX", "sys_modify_time");
		mapping.put("SDRID", "to_receive_person_uuid");
		mapping.put("XTCJSJCXBYDX", "sys_create_time");
		mapping.put("DWJC", "org_shortname");
		mapping.put("SFYJXD", "is_mail_order");
		mapping.put("SFGGSD", "is_announce_service");
		mapping.put("AJUUID", "case_uuid");
		mapping.put("TAR", "same_party");
		mapping.put("SSDRLXDH", "receive_contact_tel");
		mapping.put("XGR", "modifier");
		mapping.put("CJR", "creator");
		mapping.put("BZ", "remark");
		mapping.put("SDFSBCSM", "service_style_added");
		mapping.put("SDHZWSLX", "service_receipt_type");
		mapping.put("XFLSH", "ems_order_no");
		mapping.put("WSH", "doc_no");
		mapping.put("YWXSXWSMXBLSID", "lidd_id");
		mapping.put("ZZJGUUID", "org_uuid");
		mapping.put("AJMC", "case_name");
		mapping.put("ZMRQZRQ", "witness_date");
		mapping.put("DWSXZ", "org_abbr");
		mapping.put("CBBMUUID", "reg_dept_uuid");
		mapping.put("AJJBXXBJGJC", "org_short_name_basic");
		mapping.put("WORDWSLJ", "word_file_path");
		mapping.put("SSDR", "receive_person");
		mapping.put("ZMRQZ", "witness_name");
		mapping.put("XYWYBS", "tid");
		mapping.put("SDDD", "service_addr");
		mapping.put("SJBM", "city_org_code");
		mapping.put("ND", "doc_year");
		mapping.put("SSRQZRQ", "receiver_date");
		mapping.put("WCZT", "finsh_status");
		mapping.put("SJMC", "city_org_name");
		mapping.put("AJBH", "case_code");
		mapping.put("MCRKSJ", "mc_tec_ctime");
		mapping.put("WSHQ", "full_doc_no");
		mapping.put("AJJBXXBJGSXZ", "org_abbr_basic");
		mapping.put("QSRQ", "service_date");
		mapping.put("CHBMUUID", "get_dept_uuid");
		mapping.put("XGSJ", "modify_time");

		return mapping;
	}

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {

        Map<String, Object> mockData = new HashMap<>();
        if(type==1){
            Map<String, Object> query=new HashMap<>();

//             query.put("AJUUID", "24909616d4b042d8bb4b7e693382e9bb");
           query.put("AJUUID", caseId);
            JSONArray array = icaseInfoService.getCaseDeliveryReceiptDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if(array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
				Map<String, Object> processData = new HashMap<>();
				Map<String, String> mapper = getReverseFieldMapping();
                if(firstData != null) {
					// 处理数据
					firstData.forEach((key, value) -> {
						String newKey = mapper.get(key);
						if (StrUtil.isBlank(newKey)) {
							newKey = key;
						}

						// 对特定字段进行格式化处理
						Object processedValue = value;
						if ("WSRQ".equals(key) || "doc_date".equals(newKey)) {
							// 格式化文书日期时间戳为 "yyyy年MM月dd日" 格式
							processedValue = DictCodeConverter.formatTimestamp(value, "yyyy年MM月dd日");
						} else if ("CJSJ".equals(key) || "create_time".equals(newKey)) {
							// 格式化创建时间
							processedValue = DictCodeConverter.formatTimeValue(value, "yyyy年MM月dd日 HH:mm:ss");
						} else if ("XGSJ".equals(key) || "modify_time".equals(newKey)) {
							// 格式化修改时间
							processedValue = DictCodeConverter.formatTimeValue(value, "yyyy年MM月dd日 HH:mm:ss");
						} else if ("QSRQ".equals(key) || "service_date".equals(newKey)) {
							// 格式化送达日期
							processedValue = DictCodeConverter.formatTimeValue(value, "yyyy年MM月dd日");
						} else if ("SSRQZRQ".equals(key) || "receiver_date".equals(newKey)) {
							// 格式化受送人签字日期
							processedValue = DictCodeConverter.formatTimeValue(value, "yyyy年MM月dd日");
						} else if ("ZMRQZRQ".equals(key) || "witness_date".equals(newKey)) {
							// 格式化证明人签字日期
							processedValue = DictCodeConverter.formatTimeValue(value, "yyyy年MM月dd日");
						}

						processData.put(newKey, processedValue);
					});
                    return processData;
                }
            }
        }

        // 基础信息
        mockData.put("service_receipt_uuid", "***********-001");
        mockData.put("handle_main_uuid", "***********-001");
        mockData.put("file_uuids", "FILE-001,FILE-002");
        mockData.put("service_content", "[{\"docUuid\":\"222894debb464e6badf3b1f07e41f6a1\",\"docName\":\"烟草专卖零售许可证准予行政许可决定书\",\"docNo\":\"惠阳烟专延〔2024〕许第1364号\"},{\"docUuid\":\"2101fb2fc93649918e23c83a5919e803\",\"docName\":\"烟草专卖零售许可证（正、副本）\",\"docNo\":\"************\"}]");
        mockData.put("doc_date", "2025年06月10日");
        mockData.put("party", "梁俊强");
        mockData.put("delivery_json", "{\"doc_type\":\"行政处罚决定书\",\"doc_number\":\"博烟处﹝2025﹞第48号\"}");
        mockData.put("to_receive_person", "梁俊强");
        mockData.put("create_time", "2025年06月10日 17:15:00");
        mockData.put("doc_send_type", "直接送达");
        mockData.put("is_active", 1);
        mockData.put("receiver_name", "梁俊强（签字）");
        mockData.put("is_accept_elec_delivery", 1);
        mockData.put("sys_modify_time", "2025年06月10日 17:15:00");
        mockData.put("to_receive_person_uuid", "TRP-10086");
        mockData.put("sys_create_time", "2025年06月10日 17:15:00");
        mockData.put("org_shortname", "广东省博罗县烟草专卖局");
        mockData.put("is_mail_order", 0);
        mockData.put("is_announce_service", 0);
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("same_party", "");
        mockData.put("receive_contact_tel", "13640736270");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("creator", "蔡秋宝");
        mockData.put("remark", "当事人现场签收");
        mockData.put("service_style_added", "直接送达");
        mockData.put("service_receipt_type", "行政处罚");
        mockData.put("ems_order_no", "");
        mockData.put("doc_no", "48");
        mockData.put("lidd_id", "LIDD-20250610-001");
        mockData.put("org_uuid", "4413231030000000540");
        mockData.put("case_name", "梁俊强未在当地烟草专卖批发企业进货案");
        mockData.put("witness_date", "2025年06月10日");
        mockData.put("org_abbr", "BLZMJ");
        mockData.put("reg_dept_uuid", "4413231030000002829");
        mockData.put("org_short_name_basic", "博罗烟草");
        mockData.put("word_file_path", "/documents/2025/SR-20250610-001.docx");
        mockData.put("receive_person", "梁俊强");
        mockData.put("witness_name", "朱兆强");
        mockData.put("tid", "TID-20250610-001");
        mockData.put("service_addr", "广东省博罗县龙溪街道宫庭村龙桥大道1239号");
        mockData.put("city_org_code", "441322");
        mockData.put("doc_year", "2025");
        mockData.put("receiver_date", "2025年06月10日");
        mockData.put("finsh_status", "已完成");
        mockData.put("city_org_name", "惠州市博罗县");
        mockData.put("case_code", "博烟案﹝2025﹞第48号");
        mockData.put("mc_tec_ctime", "2025年06月10日 17:15:00");
        mockData.put("full_doc_no", "博烟处﹝2025﹞第48号");
        mockData.put("org_abbr_basic", "博罗烟草");
        mockData.put("service_date", "2025年06月10日");
        mockData.put("get_dept_uuid", "4413231030000012875");
        mockData.put("modify_time", "2025年06月10日 17:15:00");

        return mockData;
    }



}
