package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springblade.modules.hzyc.DocumentGeneration.util.DictCodeConverter;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 证据先行登记保存通知书文档生成实现类
 *
 * <AUTHOR>
 */
@Service("evidencePreservationNoticeDocument")
public class EvidenceRegistrationDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);

        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "11证据先行登记保存通知书.docx";
    }

    @Override
    public String getDocumentType() {
        return "EVIDENCE-REGISTRATION";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        mapping.put("WFGD", "illegal_require");
        mapping.put("FWZXSJGXSJ", "sysupdatetime");
        mapping.put("DW", "unit");
        mapping.put("KZZD3", "ext3");
        mapping.put("GG", "spec");
        mapping.put("FWZXSJSCBJ", "sysisdelete");
        mapping.put("XSBS", "clues_uuid");
        mapping.put("SJSSBM", "own_dept_uuid");
        mapping.put("WPDJDJBS", "goods_reg_uuid");
        mapping.put("XH", "order_index");
        mapping.put("WSH", "doc_no");
        mapping.put("AYUUID", "caseprop_ids");
        mapping.put("ZXDWSL", "branch_qty");
        mapping.put("AJMC", "case_name");
        mapping.put("QXD", "to_addr");
        mapping.put("KZZD2", "ext2");
        mapping.put("AJBH", "case_code");
        mapping.put("ZZBH", "bill_no");
        mapping.put("CHHJ", "get_node");
        mapping.put("XYWYBS", "tid");
        mapping.put("CJSJ", "create_time");
        mapping.put("LYD", "source_addr");
        mapping.put("SJSSDW", "own_org_uuid");
        mapping.put("TAR", "same_party");
        mapping.put("CJR", "creator");
        mapping.put("WPBS", "goods_uuid");
        mapping.put("SJBM", "city_org_code");
        mapping.put("XGSJ", "modify_time");
        mapping.put("DJ", "price");
        mapping.put("XGR", "modifier");
        mapping.put("JE", "amt");
        mapping.put("CBRUUIDS", "undertaker_uuids");
        mapping.put("DJSJ", "reg_time");
        mapping.put("WPFL", "goods_type");
        mapping.put("PM", "goods_name");
        mapping.put("BCQX", "shelf_life");
        mapping.put("SL", "qty");
        mapping.put("SFYX", "is_active");
        mapping.put("SFYJFSJCKXTL", "send_store");
        mapping.put("KZZD1", "ext1");
        mapping.put("SQFYDW", "recheck_org");
        mapping.put("WFTK", "illegal_clause");
        mapping.put("TTXM", "strip_code");
        mapping.put("BZ", "memo");
        mapping.put("CKYDLSDM", "agent_code");
        mapping.put("XTCJSJ", "sys_create_time");
        mapping.put("JGSX", "org_abbr");
        mapping.put("AY", "cause_of_action");
        mapping.put("WPMXBS", "goods_dtl_uuid");
        mapping.put("DWZSL", "unit_qty");
        mapping.put("WSHQ", "full_doc_no");
        mapping.put("SJMC", "city_org_name");
        mapping.put("AJBS", "case_uuid");
        mapping.put("XTGXSJ", "sys_modify_time");
        mapping.put("DSR", "party");
        mapping.put("SXXZZFL", "prop_subtype");
        mapping.put("HJJE", "sum_amt");
        mapping.put("CBRZFZH", "undertaker_insp_no");
        mapping.put("XBPBS", "goods_apv_uuid");
        mapping.put("JZSJ", "witness_time");
        mapping.put("JGJC", "org_short_name");
        mapping.put("MSBTY", "is_free_stickers");
        mapping.put("SXXZFL", "prop_type");
        mapping.put("WSZT", "finsh_status");
        mapping.put("HJSL", "sum_qty");
        mapping.put("MCRKSJ", "mc_tec_ctime");
        mapping.put("FYMC", "court_name");
        mapping.put("CBR", "undertaker");
        mapping.put("JZR", "witness");
        mapping.put("ND", "doc_year");

        return mapping;
    }

    /**
     * 判断是否为物品相关字段
     */
    private boolean isGoodsField(String key) {
        return "PM".equals(key) || "GG".equals(key) || "DW".equals(key) ||
               "SL".equals(key) || "DJ".equals(key) || "JE".equals(key) ||
               "XH".equals(key) || "BZ".equals(key) || "WPBS".equals(key) ||
               "WPFL".equals(key) || "WPMXBS".equals(key);
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {
        Map<String, Object> mockData = new HashMap<>();

        if(type == 1) {
            Map<String, Object> query = new HashMap<>();
            query.put("AJBS", caseId);
            JSONArray array = icaseInfoService.getEvidencePreservationNoticeDailyReport(query);

            System.out.println("证据先行登记保存通知书 - 接口返回数据: " + array);
            System.out.println("数据类型: " + (array != null ? array.getClass().getSimpleName() : "null"));
            System.out.println("数据大小: " + (array != null ? array.size() : 0));

            // 如果array不为空，将第一条数据传给mockData
            if(array != null && array.size() > 0) {
                Map<String, String> mapper = getReverseFieldMapping();

                // 处理第一条记录的基础信息
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                Map<String, Object> processData = new HashMap<>();

                if(firstData != null) {
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }

                        // 跳过物品相关字段，这些将在goodsList中处理
                        if (!isGoodsField(key)) {
                            // 对特定字段进行格式化处理
                            Object processedValue = value;
                            if ("DJSJ".equals(key) || "reg_time".equals(newKey)) {
                                // 格式化登记时间为 "yyyy-MM-dd" 格式
                                processedValue = DictCodeConverter.formatTimestamp(value, "yyyy-MM-dd");
                            } else if ("JZSJ".equals(key) || "witness_time".equals(newKey)) {
                                // 格式化见证时间为 "yyyy-MM-dd HH:mm:ss" 格式
                                processedValue = DictCodeConverter.formatTimestamp(value, "yyyy-MM-dd HH:mm:ss");
                            }

                            processData.put(newKey, processedValue);
                        }
                    });
                }

                // 处理物品清单数据 - 用于动态表格 ${table:goodsList}
                List<Map<String, Object>> goodsList = new ArrayList<>();
                int totalVarieties = 0;
                double totalQuantity = 0.0;

                for (int i = 0; i < array.size(); i++) {
                    Map<String, Object> item = (Map<String, Object>) array.get(i);
                    Map<String, Object> goodsItem = new HashMap<>();

                    // 映射物品相关字段，确保所有值都不为null
                    goodsItem.put("goods_name", item.get("PM") != null ? item.get("PM").toString() : ""); // 品名
                    goodsItem.put("spec", item.get("GG") != null ? item.get("GG").toString() : ""); // 规格
                    goodsItem.put("unit", item.get("DW") != null ? item.get("DW").toString() : ""); // 单位
                    goodsItem.put("qty", item.get("SL") != null ? String.format("%.2f", Double.parseDouble(item.get("SL").toString())) : "0.00"); // 数量
                    goodsItem.put("price", item.get("DJ") != null ? String.format("%.2f", Double.parseDouble(item.get("DJ").toString())) : "0.00"); // 单价
                    goodsItem.put("amt", item.get("JE") != null ? String.format("%.2f", Double.parseDouble(item.get("JE").toString())) : "0.00"); // 金额
                    goodsItem.put("order_index", item.get("XH") != null ? item.get("XH").toString() : String.valueOf(i + 1)); // 序号
                    goodsItem.put("memo", item.get("BZ") != null ? item.get("BZ").toString() : ""); // 备注

                    goodsList.add(goodsItem);

                    // 统计总数
                    totalVarieties++;
                    if (item.get("SL") != null) {
                        try {
                            totalQuantity += Double.parseDouble(item.get("SL").toString());
                        } catch (NumberFormatException e) {
                            // 忽略格式错误
                        }
                    }
                }

                // 关键：使用 goodsList 作为动态表格的数据源，对应模板中的 ${table:goodsList}
                processData.put("goodsList", goodsList);
                processData.put("total_varieties", totalVarieties);
                processData.put("total_quantity", String.format("%.2f", totalQuantity));

                // 为兼容性添加前几个物品的字段（用于简单模板）
                if (!goodsList.isEmpty()) {
                    Map<String, Object> firstGoods = goodsList.get(0);
                    processData.put("goods_name", firstGoods.get("goods_name"));
                    processData.put("spec", firstGoods.get("spec"));
                    processData.put("unit", firstGoods.get("unit"));
                    processData.put("qty", firstGoods.get("qty"));
                    processData.put("price", firstGoods.get("price"));
                    processData.put("amt", firstGoods.get("amt"));
                    processData.put("memo", firstGoods.get("memo"));

                    // 添加前10个物品的单独字段（用于表格模板）
                    for (int i = 0; i < Math.min(10, goodsList.size()); i++) {
                        Map<String, Object> goods = goodsList.get(i);
                        processData.put("goods_name_" + (i + 1), goods.get("goods_name"));
                        processData.put("spec_" + (i + 1), goods.get("spec"));
                        processData.put("unit_" + (i + 1), goods.get("unit"));
                        processData.put("qty_" + (i + 1), goods.get("qty"));
                        processData.put("price_" + (i + 1), goods.get("price"));
                        processData.put("amt_" + (i + 1), goods.get("amt"));
                        processData.put("memo_" + (i + 1), goods.get("memo"));
                    }
                }

                // 使用真实数据时，直接返回处理后的数据
                return processData;
            } else {
                System.out.println("证据先行登记保存通知书 - 接口返回数据为空或无效，使用模拟数据");
            }
        }

        System.out.println("证据先行登记保存通知书 - 使用模拟数据，数据类型: " + type + ", 案件ID: " + caseId);

        // 基础信息
        mockData.put("goods_reg_uuid", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("goods_apv_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("clues_uuid", "7469e787e35748e2c935d2f473785300");

        // 机构信息
        mockData.put("org_short_name", "广东省博罗县烟草专卖局");
        mockData.put("org_abbr", "博罗县局");

        // 文书信息
        mockData.put("doc_year", 2025);
        mockData.put("doc_no", "博烟先保﹝2025﹞第48号");
        mockData.put("full_doc_no", "博烟先保﹝2025﹞第48号");

        // 当事人信息
        mockData.put("party", "梁俊强");
        mockData.put("same_party", "");

        // 案件信息
        mockData.put("case_name", "梁俊强涉嫌未在当地烟草专卖批发企业进货案");
        mockData.put("case_code", "博烟案﹝2025﹞第48号");
        mockData.put("cause_of_action", "未在当地烟草专卖批发企业进货");

        // 见证人和承办人信息
        mockData.put("witness", "张三、李四");
        mockData.put("undertaker_uuids", "19090352015,19090352023");
        mockData.put("undertaker", "叶辉明、朱兆强");
        mockData.put("undertaker_insp_no", "19090352015,19090352023");

        // 时间信息
        mockData.put("reg_time", "2025-03-18");
        mockData.put("witness_time", "2025-03-18 14:30:00");
        mockData.put("doc_date", "2025年03月18日");

        // 送达信息
        mockData.put("service_style", "直接送达");
        mockData.put("service_addr", "广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号");

        // 物品信息
        mockData.put("bill_no", "XB202503180001");
        mockData.put("sum_qty", 1075.0);
        mockData.put("sum_amt", 108625.00);
        mockData.put("total_varieties", 17);
        mockData.put("total_quantity", "1075.00");

        // 法律条款
        mockData.put("illegal_clause", "《中华人民共和国烟草专卖法实施条例》第五十六条");
        mockData.put("illegal_require", "取得烟草专卖零售许可证的企业或者个人违反本条例第二十三条第二款的规定，未在当地烟草专卖批发企业进货的");

        // 保存期限和备注
        mockData.put("shelf_life", "三年");
        mockData.put("memo", "涉案卷烟共17个品牌规格合计1075条，已按规定先行登记保存");

        // 复议和诉讼信息
        mockData.put("recheck_org", "广东省惠州市烟草专卖局或博罗县人民政府");
        mockData.put("court_name", "惠州市惠城区人民法院");

        // 状态信息
        mockData.put("finsh_status", "已完成");
        mockData.put("send_store", 1);

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025-03-18 14:30:00");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025-03-18 14:30:00");
        mockData.put("sysisdelete", "");
        mockData.put("sysupdatetime", "");
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");
        mockData.put("sys_create_time", "2025-03-18 14:30:00");
        mockData.put("sys_modify_time", "2025-03-18 14:30:00");

        // 案由相关
        mockData.put("caseprop_ids", "prop_001,prop_002");

        // 行业标识
        mockData.put("tid", "TID202503180001");

        // 扩展字段
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");

        // 市局信息
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");
        mockData.put("mc_tec_ctime", "2025-03-18 14:30:00");

        // 物品明细列表 - 用于动态表格 ${table:goodsList}
        List<Map<String, Object>> goodsList = new ArrayList<>();

        // 明细1
        Map<String, Object> goods1 = new HashMap<>();
        goods1.put("goods_dtl_uuid", "dtl_001");
        goods1.put("order_index", 1);
        goods1.put("goods_type", "卷烟");
        goods1.put("goods_uuid", "goods_001");
        goods1.put("goods_name", "黄果树(长征)");
        goods1.put("spec", "20支/包，10包/条");
        goods1.put("unit", "条");
        goods1.put("price", 45.0);
        goods1.put("qty", 200.0);
        goods1.put("amt", 9000.0);
        goods1.put("prop_type", "违法经营");
        goods1.put("prop_subtype", "未在当地批发企业进货");
        goods1.put("unit_qty", 20);
        goods1.put("branch_qty", 4000.0);
        goods1.put("to_addr", "博罗县龙溪街道");
        goods1.put("source_addr", "未知");
        goods1.put("agent_code", "");
        goods1.put("is_free_stickers", 0);
        goods1.put("get_node", "零售环节");
        goods1.put("strip_code", "6901028300018");
        goodsList.add(goods1);

        // 明细2
        Map<String, Object> goods2 = new HashMap<>();
        goods2.put("goods_dtl_uuid", "dtl_002");
        goods2.put("order_index", 2);
        goods2.put("goods_type", "卷烟");
        goods2.put("goods_uuid", "goods_002");
        goods2.put("goods_name", "白沙(硬精品三代)");
        goods2.put("spec", "20支/包，10包/条");
        goods2.put("unit", "条");
        goods2.put("price", 35.0);
        goods2.put("qty", 150.0);
        goods2.put("amt", 5250.0);
        goods2.put("prop_type", "违法经营");
        goods2.put("prop_subtype", "未在当地批发企业进货");
        goods2.put("unit_qty", 20);
        goods2.put("branch_qty", 3000.0);
        goods2.put("to_addr", "博罗县龙溪街道");
        goods2.put("source_addr", "未知");
        goods2.put("agent_code", "");
        goods2.put("is_free_stickers", 0);
        goods2.put("get_node", "零售环节");
        goods2.put("strip_code", "6901028080019");
        goodsList.add(goods2);

        // 明细3
        Map<String, Object> goods3 = new HashMap<>();
        goods3.put("goods_dtl_uuid", "dtl_003");
        goods3.put("order_index", 3);
        goods3.put("goods_type", "卷烟");
        goods3.put("goods_uuid", "goods_003");
        goods3.put("goods_name", "红塔山(硬经典)");
        goods3.put("spec", "20支/包，10包/条");
        goods3.put("unit", "条");
        goods3.put("price", 30.0);
        goods3.put("qty", 150.0);
        goods3.put("amt", 4500.0);
        goods3.put("prop_type", "违法经营");
        goods3.put("prop_subtype", "未在当地批发企业进货");
        goods3.put("unit_qty", 20);
        goods3.put("branch_qty", 3000.0);
        goods3.put("to_addr", "博罗县龙溪街道");
        goods3.put("source_addr", "未知");
        goods3.put("agent_code", "");
        goods3.put("is_free_stickers", 0);
        goods3.put("get_node", "零售环节");
        goods3.put("strip_code", "6901028080026");
        goodsList.add(goods3);

        // 将物品明细列表添加到主数据中 - 关键：使用 goodsList 作为动态表格的数据源
        mockData.put("goodsList", goodsList);

        // 为兼容性添加前几个物品的字段（用于简单模板）
        if (!goodsList.isEmpty()) {
            Map<String, Object> firstGoods = goodsList.get(0);
            mockData.put("goods_name", firstGoods.get("goods_name"));
            mockData.put("spec", firstGoods.get("spec"));
            mockData.put("unit", firstGoods.get("unit"));
            mockData.put("qty", String.format("%.2f", (Double) firstGoods.get("qty")));
            mockData.put("price", String.format("%.2f", (Double) firstGoods.get("price")));
            mockData.put("amt", String.format("%.2f", (Double) firstGoods.get("amt")));

            // 添加前10个物品的单独字段（用于表格模板）
            for (int i = 0; i < Math.min(10, goodsList.size()); i++) {
                Map<String, Object> goods = goodsList.get(i);
                mockData.put("goods_name_" + (i + 1), goods.get("goods_name"));
                mockData.put("spec_" + (i + 1), goods.get("spec"));
                mockData.put("unit_" + (i + 1), goods.get("unit"));
                mockData.put("qty_" + (i + 1), String.format("%.2f", (Double) goods.get("qty")));
                mockData.put("price_" + (i + 1), String.format("%.2f", (Double) goods.get("price")));
                mockData.put("amt_" + (i + 1), String.format("%.2f", (Double) goods.get("amt")));
            }
        }

        return mockData;
    }
}
