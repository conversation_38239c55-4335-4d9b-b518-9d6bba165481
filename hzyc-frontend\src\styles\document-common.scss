// 文档组件通用样式
.penalty-decision-container,
.delivery-receipt-container,
.inquiry-record-container,
.investigation-report-container,
.electronic-delivery-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.document-header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;

  .header-left h3 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }

  .header-right {
    display: flex;
    gap: 8px;
  }
}

.document-preview,
.document-body {
  flex: 1;
  overflow-y: auto;
}

.preview-content {
  min-height: 600px;
  background: white;
  border-radius: 4px;
}

.document-layout,
.document-display {
  margin: 0 auto;
  padding: 40px;
  background: white;
  line-height: 1.8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.document-title {
  text-align: center;
  margin-bottom: 50px;

  .bureau-name {
    font-size: 16px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
  }

  h2 {
    font-size: 24px;
    font-weight: bold;
    margin: 20px 0;
  }
}

.document-number {
  font-size: 14px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.content-section {
  margin-bottom: 20px;
  text-align: justify;
}

.case-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.case-label {
  font-weight: bold;
  white-space: nowrap;
}

.case-input {
  flex: 1;
}

.party-info,
.legal-fact,
.evidence-content,
.punish-decide,
.other-info {
  width: 100%;
}

.signature-section {
  position: relative;
  text-align: right;
  margin-top: 50px;

  .signature-line {
    font-size: 14px;
    margin-bottom: 20px;
  }

  .date-line {
    font-size: 14px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.underline-text {
  border-bottom: 1px solid #333;
  min-width: 60px;
  padding: 2px 5px;
  margin: 0 3px;
  display: inline-block;
  text-align: center;
}

// 送达回证特有样式
.form-table {
  margin-bottom: 50px;
}

.delivery-table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid #000;
  font-size: 14px;

  td {
    border: 1px solid #000;
    padding: 8px 12px;
    vertical-align: middle;
  }

  .label-cell {
    background-color: #f5f5f5;
    font-weight: bold;
    width: 120px;
    text-align: center;
    writing-mode: vertical-lr;
    text-orientation: upright;
  }

  .content-cell {
    background-color: white;
    min-height: 40px;
    padding: 12px;
    line-height: 1.6;
  }

  .signature-cell {
    height: 60px;
    text-align: center;
  }

  .sub-table-cell {
    padding: 0;
  }

  .sub-table {
    width: 100%;
    border-collapse: collapse;

    td {
      border: 1px solid #000;
      padding: 6px;
      font-size: 12px;
    }

    .sub-label {
      background-color: #f9f9f9;
      font-weight: bold;
      text-align: center;
      width: 100px;
    }

    .sub-content {
      text-align: left;
      min-width: 120px;
    }
  }

  .commitment-text {
    line-height: 1.6;
    text-align: justify;
  }

  .signature-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;

    .date-area {
      margin-left: auto;
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-input__inner) {
  border: 1px dashed #dcdfe6;
  background-color: #fafafa;
}

:deep(.el-textarea__inner:focus),
:deep(.el-input__inner:focus) {
  border-color: #409eff;
  background-color: white;
}

// 打印样式
@media print {
  .document-header {
    display: none;
  }
  
  .document-layout,
  .document-display {
    padding: 0;
    box-shadow: none;
    max-width: none;
  }
  
  .content-display {
    border: none;
    background-color: transparent;
    padding: 0;
  }

  .delivery-table {
    border: 2px solid #000 !important;

    td {
      border: 1px solid #000 !important;
    }
  }
}
