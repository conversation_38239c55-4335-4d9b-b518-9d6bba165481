<template>
  <div class="electronic-delivery-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>电子送达方式确认书</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.bureau_name"
                placeholder="机构简称"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>电子送达方式确认书</h2>
            </div>
          </div>

          <!-- 案号信息 -->
          <div class="content-section case-section">
            <span class="case-label">案号：</span>
            <el-input
              v-model="formData.case_no"
              placeholder="案件编号"
              class="case-input"
            />
          </div>

          <!-- 案由 -->
          <div class="content-section case-section">
            <span class="case-label">案由：</span>
            <el-input
              v-model="formData.case_reason"
              placeholder="案件性质"
              class="case-input"
            />
          </div>

          <!-- 告知事项 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.notification_content"
                type="textarea"
                :autosize="{ minRows: 6 }"
                placeholder="告知事项"
                class="notification-content auto-resize-textarea"
                maxlength="2000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 送达方式及凭证 -->
          <div class="form-table">
            <table class="delivery-table">
              <tr>
                <td class="label-cell" rowspan="4">送达方式及凭证</td>
                <td class="sub-table-cell">
                  <table class="sub-table">
                    <tr>
                      <td class="sub-label">受送达人</td>
                      <td class="sub-content">
                        <el-input
                          v-model="formData.recipient"
                          placeholder="受送达人姓名"
                          size="small"
                        />
                      </td>
                      <td class="sub-label">联系方式</td>
                      <td class="sub-content">
                        <el-input
                          v-model="formData.contact_method"
                          placeholder="联系方式"
                          size="small"
                        />
                      </td>
                    </tr>
                    <tr>
                      <td class="sub-label">证件类型</td>
                      <td class="sub-content">
                        <el-input
                          v-model="formData.id_type"
                          placeholder="证件类型"
                          size="small"
                        />
                      </td>
                      <td class="sub-label">证件号码</td>
                      <td class="sub-content">
                        <el-input
                          v-model="formData.id_number"
                          placeholder="证件号码"
                          size="small"
                        />
                      </td>
                    </tr>
                    <tr>
                      <td class="sub-label" rowspan="3">电子送达地址</td>
                      <td class="sub-content">
                        □手机号码：
                        <el-input
                          v-model="formData.phone_number"
                          placeholder="手机号码"
                          size="small"
                          style="display: inline-block; width: 120px; margin-left: 5px;"
                        />
                      </td>
                      <td class="sub-content">
                        □微信号：
                        <el-input
                          v-model="formData.wechat_id"
                          placeholder="微信号"
                          size="small"
                          style="display: inline-block; width: 120px; margin-left: 5px;"
                        />
                      </td>
                    </tr>
                    <tr>
                      <td class="sub-content">
                        □钉钉号：
                        <el-input
                          v-model="formData.dingtalk_no"
                          placeholder="钉钉号"
                          size="small"
                          style="display: inline-block; width: 120px; margin-left: 5px;"
                        />
                      </td>
                      <td class="sub-content">
                        □电子邮件地址：
                        <el-input
                          v-model="formData.email_address"
                          placeholder="邮箱地址"
                          size="small"
                          style="display: inline-block; width: 120px; margin-left: 5px;"
                        />
                      </td>
                    </tr>
                    <tr>
                      <td class="sub-content" colspan="2">
                        □传真号码：
                        <el-input
                          v-model="formData.fax_number"
                          placeholder="传真号码"
                          size="small"
                          style="display: inline-block; width: 120px; margin-left: 5px;"
                        />
                        □其他：
                        <el-input
                          v-model="formData.other_contact"
                          placeholder="其他联系方式"
                          size="small"
                          style="display: inline-block; width: 120px; margin-left: 5px;"
                        />
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr>
                <td class="content-cell commitment-text">
                  <el-input
                    v-model="formData.commitment"
                    type="textarea"
                    :autosize="{ minRows: 4 }"
                    placeholder="承诺内容"
                    class="commitment auto-resize-textarea"
                    maxlength="1500"
                    show-word-limit
                  />
                </td>
              </tr>
              <tr>
                <td class="label-cell">受送达人签名</td>
                <td class="signature-cell">
                  <div class="signature-area">
                    <span>受送达人（签名或盖章）：</span>
                    <span class="date-area">年 月 日</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="label-cell">案件承办人员签名</td>
                <td class="signature-cell">
                  <div class="signature-area">
                    <span class="date-area">年 月 日</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="label-cell">备注</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.remarks"
                    type="textarea"
                    :autosize="{ minRows: 3 }"
                    placeholder="备注信息"
                    class="remarks auto-resize-textarea"
                    maxlength="1000"
                    show-word-limit
                  />
                </td>
              </tr>
            </table>
          </div>

          <!-- 签名区域 -->
          <div class="signature-section">
            <div class="signature-line">
              <span>落款（印章）</span>
            </div>
            <div class="date-line">
              <el-input
                v-model="formData.sys_modify_time"
                placeholder="日期"
                style="width: 200px;"
              />
            </div>
          </div>
        </div>
      </template>

    </div>
  </div>

</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  bureau_name: '惠阳区',
  case_no: '',
  case_reason: '',
  notification_content: `1. 为提高送达效率，本局可以采用传真、电子邮件、短信、微信、钉钉等电子方式送达法律文书，以送达方设备显示送达成功时间为送达。
2. 为保证当事人及时知悉相关内容，保证行政执法程序的正当性，当事人应当如实提供电子送达方式的相关信息，号码等信息如有变更，应及时告知。
3. 确认的送达地址适用于行政执法程序、听证程序、执行程序，如果送达地址有变更，应当及时告知本局受送达人的送达地址。
4. 受送达人电子送达地址不详或者无法切实者不及时告知变更的地址，依法律文书无法送达或者未及时送达，当事人自行承担由此产生的法律后果。`,
  recipient: '',
  contact_method: '',
  id_type: '',
  id_number: '',
  phone_number: '',
  wechat_id: '',
  dingtalk_no: '',
  email_address: '',
  fax_number: '',
  other_contact: '',
  commitment: '我已阅读（听明白）本确认书的告知事项，自愿选择电子送达方式并提供相应地址，并保证所提供的约定内容真实、有效，如有行政处罚决定书等送达地址发生变更，将及时通知贵局，未及时告知变更的，按上述地址送达即发生法律效力。',
  remarks: '',
  sys_modify_time: ''
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      bureau_name: docContent.bureau_name || docContent.bureauName || newVal.bureau_name || '',
      case_no: docContent.case_no || docContent.caseNo || newVal.case_no || '',
      case_reason: docContent.case_reason || docContent.caseReason || newVal.case_reason || '',
      notification_content: docContent.notification_content || docContent.notificationContent || newVal.notification_content || '',
      recipient: docContent.recipient || newVal.recipient || '',
      contact_method: docContent.contact_method || docContent.contactMethod || newVal.contact_method || '',
      id_type: docContent.id_type || docContent.idType || newVal.id_type || '',
      id_number: docContent.id_number || docContent.idNumber || newVal.id_number || '',
      phone_number: docContent.phone_number || docContent.phoneNumber || newVal.phone_number || '',
      wechat_id: docContent.wechat_id || docContent.wechatId || newVal.wechat_id || '',
      dingtalk_no: docContent.dingtalk_no || docContent.dingtalkNo || newVal.dingtalk_no || '',
      email_address: docContent.email_address || docContent.emailAddress || newVal.email_address || '',
      fax_number: docContent.fax_number || docContent.faxNumber || newVal.fax_number || '',
      other_contact: docContent.other_contact || docContent.otherContact || newVal.other_contact || '',
      commitment: docContent.commitment || newVal.commitment || '',
      remarks: docContent.remarks || newVal.remarks || '',
      sys_modify_time: docContent.sys_modify_time || docContent.sysModifyTime || newVal.sys_modify_time || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    bureau_name: formData.value.bureau_name,
    case_no: formData.value.case_no,
    case_reason: formData.value.case_reason,
    notification_content: formData.value.notification_content,
    recipient: formData.value.recipient,
    contact_method: formData.value.contact_method,
    id_type: formData.value.id_type,
    id_number: formData.value.id_number,
    phone_number: formData.value.phone_number,
    wechat_id: formData.value.wechat_id,
    dingtalk_no: formData.value.dingtalk_no,
    email_address: formData.value.email_address,
    fax_number: formData.value.fax_number,
    other_contact: formData.value.other_contact,
    commitment: formData.value.commitment,
    remarks: formData.value.remarks,
    sys_modify_time: formData.value.sys_modify_time
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '文档'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'lawBasis' || action === 'penalty') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */
</style>
