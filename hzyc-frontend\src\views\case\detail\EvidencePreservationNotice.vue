<template>
  <div class="evidence-preservation-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>证据先行登记保存通知书</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.org_short_name"
                placeholder="机构名称"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>证据先行登记保存通知书</h2>
            </div>

            <div class="document-number">
              <el-input
                v-model="formData.full_doc_no"
                placeholder="文书编号"
                class="doc-number-input"
              />
            </div>
          </div>

          <!-- 当事人信息 -->
          <div class="content-section">
            <div class="party-section">
              <span class="party-label">
                <el-input
                  v-model="formData.party"
                  placeholder="当事人"
                  style="width: 200px;"
                />
              </span>
              <span>：</span>
            </div>
          </div>

          <!-- 法律依据 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.cause_of_action"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="因你（单位）涉嫌${cause_of_action}的行为，违反了《中华人民共和国烟草专卖法》及实施条例的有关规定，根据《中华人民共和国行政处罚法》第五十六条的规定，本局决定对下列证据予以先行登记保存："
                class="legal-basis auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 证据清单表格 -->
          <div class="evidence-table-container">
            <table class="evidence-table">
              <tbody>
                <!-- 表格头部 -->
                <tr class="header-row">
                  <td class="header-cell">品种规格</td>
                  <td class="header-cell">单位</td>
                  <td class="header-cell">数量</td>
                </tr>

                <!-- 证据数据行 -->
                <tr v-for="(evidence, index) in formData.goodsList" :key="index">
                  <td class="data-cell">
                    <el-input v-model="evidence.goods_name" size="small" placeholder="品种规格" />
                  </td>
                  <td class="data-cell">
                    <el-input v-model="evidence.unit" size="small" placeholder="单位" />
                  </td>
                  <td class="data-cell">
                    <el-input v-model="evidence.qty" size="small" placeholder="数量" />
                  </td>
                </tr>

                <!-- 添加/删除证据按钮行 -->
                <tr>
                  <td colspan="3" style="text-align: center; padding: 10px;">
                    <el-button type="primary" size="small" @click="addEvidenceRow">添加证据</el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeEvidenceRow(formData.goodsList.length - 1)"
                      :disabled="formData.goodsList.length <= 1"
                      style="margin-left: 10px;">
                      删除最后一行
                    </el-button>
                  </td>
                </tr>

                <!-- 合计行 -->
                <tr class="total-row">
                  <td class="label-cell">共计：</td>
                  <td class="content-cell">
                    <el-input v-model="formData.total_varieties" size="small" placeholder="品种数" />
                    <span>（品种）</span>
                  </td>
                  <td class="content-cell">
                    <el-input v-model="formData.total_quantity" size="small" placeholder="总数量" />
                    <span>（数量）</span>
                  </td>
                </tr>

                <!-- 备注行 -->
                <tr class="remark-row">
                  <td class="label-cell">备注</td>
                  <td class="remark-content" colspan="2">
                    <el-input
                      v-model="formData.memo"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入备注信息"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 处理期限说明 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.processing_notice"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="对先行登记保存的证据，应当在七日内及时作出处理决定，当事人（签名）："
                class="processing-notice auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 签名区域 -->
          <div class="signature-section">
            <!-- 当事人签名 -->
            <div class="signature-row">
              <span class="signature-label">当事人（签名）：</span>
              <el-input v-model="formData.party_signature" size="small" style="width: 200px;" placeholder="当事人签名" />
              <span class="date-text">
                <el-input v-model="formData.doc_date" size="small" style="width: 100px;" placeholder="年月日" />
              </span>
            </div>

            <!-- 见证人签名 -->
            <div class="signature-row">
              <span class="signature-label">见证人（签名）：</span>
              <el-input v-model="formData.witness_signature" size="small" style="width: 200px;" placeholder="见证人签名" />
              <span class="date-text">
                <el-input v-model="formData.doc_date" size="small" style="width: 100px;" placeholder="年月日" />
              </span>
            </div>

            <!-- 承办人签名 -->
            <div class="signature-row">
              <span class="signature-label">承办人（签名）：</span>
              <el-input v-model="formData.undertaker" size="small" style="width: 120px;" placeholder="承办人" />
              <span class="signature-label" style="margin-left: 30px;">执法证号：</span>
              <el-input v-model="formData.undertaker_insp_no" size="small" style="width: 120px;" placeholder="执法证号" />
              <span class="date-text">
                <el-input v-model="formData.doc_date" size="small" style="width: 100px;" placeholder="年月日" />
              </span>
            </div>

            <div class="signature-row">
              <span class="signature-label" style="margin-left: 120px;"></span>
              <el-input v-model="formData.undertaker2" size="small" style="width: 120px;" placeholder="承办人2" />
              <span class="signature-label" style="margin-left: 30px;">执法证号：</span>
              <el-input v-model="formData.undertaker2_insp_no" size="small" style="width: 120px;" placeholder="执法证号" />
              <span class="date-text">
                <el-input v-model="formData.doc_date" size="small" style="width: 100px;" placeholder="年月日" />
              </span>
            </div>

            <!-- 送达方式 -->
            <div class="delivery-section">
              <div class="delivery-row">
                <span class="delivery-label">送达方式：</span>
                <el-input v-model="formData.service_style" size="small" style="width: 150px;" placeholder="送达方式" />
                <span class="delivery-label" style="margin-left: 30px;">送达地点：</span>
                <el-input v-model="formData.service_addr" size="small" style="width: 200px;" placeholder="送达地点" />
              </div>
            </div>

            <!-- 落款 -->
            <div class="footer-section">
              <div class="stamp-area">
                <span>落款（印章）</span>
              </div>
              <div class="date-area">
                <span>年</span>
                <el-input v-model="formData.year" size="small" style="width: 60px;" placeholder="年" />
                <span>月</span>
                <el-input v-model="formData.month" size="small" style="width: 60px;" placeholder="月" />
                <span>日</span>
                <el-input v-model="formData.day" size="small" style="width: 60px;" placeholder="日" />
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  org_short_name: '广东省博罗县烟草专卖局',
  full_doc_no: '',
  party: '',
  cause_of_action: '因你（单位）涉嫌${cause_of_action}的行为，违反了《中华人民共和国烟草专卖法》及实施条例的有关规定，根据《中华人民共和国行政处罚法》第五十六条的规定，本局决定对下列证据予以先行登记保存：',
  goodsList: [
    {
      goods_name: '',
      unit: '',
      qty: ''
    }
  ],
  total_varieties: '',
  total_quantity: '',
  memo: '',
  processing_notice: '对先行登记保存的证据，应当在七日内及时作出处理决定，当事人（签名）：',
  party_signature: '',
  witness_signature: '',
  undertaker: '',
  undertaker_insp_no: '',
  undertaker2: '',
  undertaker2_insp_no: '',
  service_style: '',
  service_addr: '',
  doc_date: '',
  year: '',
  month: '',
  day: '',
  sys_modify_time: ''
})

// 预览相关状态
const previewLoading = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 处理证据清单数据
    let goodsList = []
    if (Array.isArray(docContent.goodsList) && docContent.goodsList.length > 0) {
      goodsList = docContent.goodsList
    } else if (Array.isArray(newVal.goodsList) && newVal.goodsList.length > 0) {
      goodsList = newVal.goodsList
    } else {
      // 保持默认的空证据行
      goodsList = formData.value.goodsList
    }

    // 处理承办人信息 - 解析 undertaker 字段
    const undertakerData = docContent.undertaker || newVal.undertaker || ''
    let undertaker = formData.value.undertaker
    let undertaker_insp_no = formData.value.undertaker_insp_no
    let undertaker2 = formData.value.undertaker2
    let undertaker2_insp_no = formData.value.undertaker2_insp_no

    if (undertakerData) {
      // 解析承办人信息，格式如："李孝(19090352017),郑力元(19090352029)"
      const officers = undertakerData.split(',')
      if (officers.length >= 1) {
        const match1 = officers[0].trim().match(/(.+)\((.+)\)/)
        if (match1) {
          undertaker = match1[1]
          undertaker_insp_no = match1[2]
        }
      }
      if (officers.length >= 2) {
        const match2 = officers[1].trim().match(/(.+)\((.+)\)/)
        if (match2) {
          undertaker2 = match2[1]
          undertaker2_insp_no = match2[2]
        }
      }
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      org_short_name: docContent.org_short_name || newVal.org_short_name || formData.value.org_short_name,
      full_doc_no: docContent.full_doc_no || newVal.full_doc_no || formData.value.full_doc_no,
      party: docContent.party || newVal.party || formData.value.party,
      cause_of_action: docContent.cause_of_action || newVal.cause_of_action || formData.value.cause_of_action,
      goodsList: goodsList,
      total_varieties: docContent.total_varieties || newVal.total_varieties || formData.value.total_varieties,
      total_quantity: docContent.total_quantity || newVal.total_quantity || formData.value.total_quantity,
      memo: docContent.memo || newVal.memo || formData.value.memo,
      processing_notice: docContent.processing_notice || newVal.processing_notice || formData.value.processing_notice,
      party_signature: docContent.party_signature || newVal.party_signature || formData.value.party_signature,
      witness_signature: docContent.witness_signature || newVal.witness_signature || formData.value.witness_signature,
      undertaker: undertaker,
      undertaker_insp_no: undertaker_insp_no,
      undertaker2: undertaker2,
      undertaker2_insp_no: undertaker2_insp_no,
      service_style: docContent.service_style || newVal.service_style || formData.value.service_style,
      service_addr: docContent.service_addr || newVal.service_addr || formData.value.service_addr,
      doc_date: docContent.doc_date || newVal.doc_date || formData.value.doc_date,
      sys_modify_time: docContent.sys_modify_time || newVal.sys_modify_time || formData.value.sys_modify_time
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    org_short_name: formData.value.org_short_name,
    full_doc_no: formData.value.full_doc_no,
    party: formData.value.party,
    cause_of_action: formData.value.cause_of_action,
    goodsList: formData.value.goodsList,
    total_varieties: formData.value.total_varieties,
    total_quantity: formData.value.total_quantity,
    memo: formData.value.memo,
    processing_notice: formData.value.processing_notice,
    party_signature: formData.value.party_signature,
    witness_signature: formData.value.witness_signature,
    undertaker: formData.value.undertaker,
    undertaker_insp_no: formData.value.undertaker_insp_no,
    undertaker2: formData.value.undertaker2,
    undertaker2_insp_no: formData.value.undertaker2_insp_no,
    service_style: formData.value.service_style,
    service_addr: formData.value.service_addr,
    doc_date: formData.value.doc_date,
    sys_modify_time: formData.value.sys_modify_time || new Date().getTime()
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 新增证据行
const addEvidenceRow = () => {
  formData.value.goodsList.push({
    goods_name: '',
    unit: '',
    qty: ''
  })
}

// 删除证据行
const removeEvidenceRow = (index) => {
  if (formData.value.goodsList.length > 1) {
    formData.value.goodsList.splice(index, 1)
  }
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '证据先行登记保存通知书'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'evidenceList' || action === 'officerInfo') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */

/* 证据先行登记保存通知书特有样式 */
.evidence-preservation-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.evidence-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.evidence-list-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.evidence-table-container {
  margin-bottom: 30px;
  overflow-x: auto;
}

.evidence-table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid #333;
  font-size: 14px;
}

.evidence-table th,
.evidence-table td {
  border: 1px solid #333;
  padding: 8px;
  text-align: center;
  vertical-align: middle;
}

.evidence-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.evidence-table td {
  min-height: 40px;
}

.evidence-table .el-input {
  width: 100%;
}

.evidence-table .el-input__wrapper {
  border: none;
  box-shadow: none;
  background: transparent;
}

.evidence-table .el-input__inner {
  text-align: center;
}

/* 证据信息表格样式 */
.evidence-table-container {
  margin: 30px 0;
}

.evidence-table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid #333;
}

.evidence-table td {
  border: 1px solid #333;
  padding: 8px;
  vertical-align: middle;
  min-height: 40px;
}

.label-cell {
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center;
  width: 100px;
}

.content-cell {
  padding: 8px 12px;
  text-align: left;
}

.header-row {
  background-color: #f0f0f0;
}

.header-cell {
  background-color: #f0f0f0;
  font-weight: bold;
  text-align: center;
  padding: 12px 8px;
}

.data-cell {
  text-align: center;
  padding: 8px;
}

.total-row {
  background-color: #f9f9f9;
}

.remark-row {
  height: 100px;
}

.remark-content {
  text-align: left;
  vertical-align: top;
  padding: 10px;
}

.signature-section {
  margin-top: 50px;
}

.signature-row {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  line-height: 2;
}

.signature-label {
  font-weight: 500;
  margin-right: 10px;
  white-space: nowrap;
}

.date-text {
  margin-left: 20px;
  color: #666;
}

.delivery-section {
  margin: 30px 0;
}

.delivery-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.delivery-label {
  font-weight: 500;
  margin-right: 10px;
  white-space: nowrap;
}

.footer-section {
  margin-top: 50px;
  text-align: right;
}

.stamp-area {
  margin-bottom: 20px;
  font-size: 16px;
}

.date-area {
  font-size: 16px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 5px;
}

/* 确保输入框在表格中的样式 */
.evidence-table .el-input {
  width: 100%;
}

.evidence-table .el-input__wrapper {
  border: none;
  box-shadow: none;
  background: transparent;
}

.evidence-table .el-input__inner {
  text-align: center;
}

/* 打印样式 */
@media print {
  .document-header {
    display: none;
  }

  .evidence-list-header .el-button {
    display: none;
  }

  .evidence-table th:last-child,
  .evidence-table td:last-child {
    display: none;
  }

  .document-layout {
    padding: 0;
    box-shadow: none;
  }
}
</style>
